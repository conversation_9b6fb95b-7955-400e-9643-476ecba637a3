import { <PERSON>rowser, BrowserContext, chromium, Page } from 'playwright';
import type { BrowserContextOptions } from 'playwright';
import * as path from 'path';
import * as fs from 'fs';
import * as os from 'os';
import { BrowserConfig } from './browser-config';
import { getConcurrencyConfig, getHealthConfig, getMonitoringConfig } from '../config'; // V2.0 引入配置管理

export type Platform = 'taobao' | 'xiaohongshu';

/**
 * 按照PRD要求的双平台爬虫系统
 * 核心特性：
 * 1. 创建两个独立的 browser.newContext() - taobaoContext 和 xiaohongshuContext
 * 2. 每个平台最多同时打开 5 个 page 实例
 * 3. 使用 context.storageState() 保存登录状态，避免重复扫码
 * 4. 支持滑块验证暂停等待机制
 * 5. 任务完成后关闭 context 与 browser 实例
 */
export class BrowserManager {
  private browser: Browser | null = null;
  private taobaoContext: BrowserContext | null = null;
  private xiaohongshuContext: BrowserContext | null = null;
  private storageStateDir: string;
  
  // V2.0 页面池管理 - 使用配置管理器替代魔法数字
  private taobaoPages: Set<Page> = new Set();
  private xiaohongshuPages: Set<Page> = new Set();
  private readonly MAX_PAGES_PER_PLATFORM = getConcurrencyConfig().maxPagesPerPlatform;

  constructor(storagePath: string) {
    // 存储登录状态的目录
    this.storageStateDir = storagePath;
    this.ensureStorageDir();
  }

  /**
   * 确保存储目录存在
   */
  private ensureStorageDir(): void {
    if (!fs.existsSync(this.storageStateDir)) {
      fs.mkdirSync(this.storageStateDir, { recursive: true });
    }
  }

  /**
   * 获取平台登录状态文件路径
   */
  private getStorageStatePath(platform: Platform): string {
    return path.join(this.storageStateDir, `${platform}-auth.json`);
  }

  /**
   * 启动浏览器并创建双平台独立context
   * @param headless 是否使用headless模式，默认true（应用初始化时使用）
   * @param forTask 是否为任务启动，如果是则强制使用visible模式
   */
  public async launch(headless: boolean = true, forTask: boolean = false): Promise<void> {
    if (this.browser) {
      console.log('[BrowserManager] Browser already launched');
      return;
    }

    // 🎯 核心逻辑：任务时强制使用visible模式，初始化时使用headless模式
    const actualHeadless = forTask ? false : headless;

    try {
      console.log(`[BrowserManager] 🚀 启动浏览器... (${forTask ? '任务模式 - visible' : actualHeadless ? 'headless' : 'visible'})`);

      // 获取浏览器启动配置 - 根据实际模式设置
      const launchOptions = BrowserConfig.getBrowserLaunchOptions(actualHeadless);
      console.log('[BrowserManager] 🔧 浏览器配置:', JSON.stringify(launchOptions, null, 2));

      if (!actualHeadless) {
        console.log('[BrowserManager] 👁️ 启用可视化模式 - 浏览器窗口将显示');
      } else {
        console.log('[BrowserManager] 🔇 启用headless模式 - 后台运行');
      }

      // 启动浏览器实例
      this.browser = await chromium.launch(launchOptions);

      console.log('[BrowserManager] ✅ 浏览器启动成功');

      // 创建淘宝独立context
      await this.createTaobaoContext();

      // 创建小红书独立context
      await this.createXiaohongshuContext();

      console.log('[BrowserManager] ✅ 双平台独立context创建完成');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('[BrowserManager] ❌ 浏览器启动失败:', errorMessage);

      // 提供用户友好的错误信息
      if (errorMessage.includes('executable') || errorMessage.includes('not found')) {
        throw new Error('浏览器组件未安装，请检查 Playwright 安装状态');
      }

      throw error;
    }
  }

  /**
   * 创建淘宝独立context
   */
  private async createTaobaoContext(): Promise<void> {
    if (!this.browser) {
      throw new Error('Browser not launched');
    }

    const storagePath = this.getStorageStatePath('taobao');
    let storageState: BrowserContextOptions['storageState'] = undefined;

    // 尝试加载已保存的登录状态
    if (fs.existsSync(storagePath)) {
      try {
        const stateData = fs.readFileSync(storagePath, 'utf-8');
        storageState = JSON.parse(stateData);
        console.log('[BrowserManager] 🔄 加载淘宝登录状态');
      } catch (error) {
        console.log('[BrowserManager] ⚠️  淘宝登录状态加载失败，将创建新会话');
      }
    }

    // 创建淘宝专用context
    this.taobaoContext = await this.browser.newContext({
      storageState,
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      viewport: { width: 1366, height: 768 },
      extraHTTPHeaders: {
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      },
    });

    console.log('[BrowserManager] ✅ 淘宝context创建完成');
  }

  /**
   * 创建小红书独立context
   */
  private async createXiaohongshuContext(): Promise<void> {
    if (!this.browser) {
      throw new Error('Browser not launched');
    }

    const storagePath = this.getStorageStatePath('xiaohongshu');
    let storageState: BrowserContextOptions['storageState'] = undefined;

    // 尝试加载已保存的登录状态
    if (fs.existsSync(storagePath)) {
      try {
        const stateData = fs.readFileSync(storagePath, 'utf-8');
        storageState = JSON.parse(stateData);
        console.log('[BrowserManager] 🔄 加载小红书登录状态');
      } catch (error) {
        console.log('[BrowserManager] ⚠️  小红书登录状态加载失败，将创建新会话');
      }
    }

    // 创建小红书专用context
    this.xiaohongshuContext = await this.browser.newContext({
      storageState,
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      viewport: { width: 1366, height: 768 },
      extraHTTPHeaders: {
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      },
    });

    console.log('[BrowserManager] ✅ 小红书context创建完成');
  }

  /**
   * 获取指定平台的页面实例
   * 实现页面池管理，每个平台最多5个页面
   */
  /**
   * 🚀 智能页面获取（带候补队列和并发控制）
   * 2.1 候补策略：每个 page 是独立页面，任务完成后关闭并从待处理队列中补充下一个
   */
  public async getPage(platform: Platform): Promise<Page> {
    const context = platform === 'taobao' ? this.taobaoContext : this.xiaohongshuContext;
    const pageSet = platform === 'taobao' ? this.taobaoPages : this.xiaohongshuPages;

    if (!context) {
      throw new Error(`${platform} context not initialized`);
    }

    // 检查页面池是否已满
    if (pageSet.size >= this.MAX_PAGES_PER_PLATFORM) {
      console.log(`[BrowserManager] ⚠️  ${platform}页面池已满 (${pageSet.size}/${this.MAX_PAGES_PER_PLATFORM})，等待页面释放...`);
      
      // 实现候补策略：等待有页面被释放
      return new Promise((resolve, reject) => {
        const checkInterval = setInterval(async () => {
          if (pageSet.size < this.MAX_PAGES_PER_PLATFORM) {
            clearInterval(checkInterval);
            try {
              const page = await this.createNewPage(context, pageSet, platform);
              resolve(page);
            } catch (error) {
              reject(error);
            }
          }
        }, 1000); // V2.0 使用1秒检查间隔

        // V2.0 使用配置的超时时间
        setTimeout(() => {
          clearInterval(checkInterval);
          reject(new Error(`${platform} page pool wait timeout`));
        }, getHealthConfig().pagePoolWaitTimeout);
      });
    }

    // 直接创建新页面
    return this.createNewPage(context, pageSet, platform);
  }

  /**
   * 创建新页面的辅助方法
   */
  private async createNewPage(context: BrowserContext, pageSet: Set<Page>, platform: Platform): Promise<Page> {
    const page = await context.newPage();
    pageSet.add(page);

    // 为页面添加标识符，便于追踪和调试
    (page as any).__platform = platform;
    (page as any).__createdAt = Date.now();
    (page as any).__taskId = `${platform}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // 监听页面关闭事件，从页面池中移除
    page.on('close', () => {
      pageSet.delete(page);
      console.log(`[BrowserManager] 📝 ${platform}页面已关闭 (ID: ${(page as any).__taskId})，当前池大小: ${pageSet.size}/${this.MAX_PAGES_PER_PLATFORM}`);
    });

    console.log(`[BrowserManager] 🆕 创建${platform}页面 (ID: ${(page as any).__taskId})，当前池大小: ${pageSet.size}/${this.MAX_PAGES_PER_PLATFORM}`);
    return page;
  }

  /**
   * 关闭指定平台的页面
   */
  public async closePage(platform: Platform, page: Page): Promise<void> {
    const pageSet = platform === 'taobao' ? this.taobaoPages : this.xiaohongshuPages;
    
    if (pageSet.has(page)) {
      await page.close();
      pageSet.delete(page);
      console.log(`[BrowserManager] ${platform} 页面已手动关闭，当前池大小: ${pageSet.size}`);
    }
  }

  /**
   * 保存指定平台的登录状态
   */
  public async saveLoginState(platform: Platform): Promise<void> {
    const context = platform === 'taobao' ? this.taobaoContext : this.xiaohongshuContext;
    
    if (!context) {
      console.warn(`[BrowserManager] ${platform} context not found, cannot save login state`);
      return;
    }

    try {
      const storageState = await context.storageState();
      const storagePath = this.getStorageStatePath(platform);
      
      fs.writeFileSync(storagePath, JSON.stringify(storageState, null, 2));
      console.log(`[BrowserManager] ✅ ${platform} 登录状态已保存`);
    } catch (error) {
      console.error(`[BrowserManager] ❌ ${platform} 登录状态保存失败:`, error);
    }
  }

  /**
   * 清除指定平台的登录状态
   */
  public async clearLoginState(platform: Platform): Promise<void> {
    const storagePath = this.getStorageStatePath(platform);
    
    if (fs.existsSync(storagePath)) {
      fs.unlinkSync(storagePath);
      console.log(`[BrowserManager] 🗑️  ${platform} 登录状态已清除`);
    }
  }

  /**
   * 获取指定平台的页面池状态
   */
  public getPagePoolStatus(platform: Platform): { current: number; max: number } {
    const pageSet = platform === 'taobao' ? this.taobaoPages : this.xiaohongshuPages;
    return {
      current: pageSet.size,
      max: this.MAX_PAGES_PER_PLATFORM,
    };
  }

  /**
   * 关闭所有资源并清理
   */
  public async close(): Promise<void> {
    console.log('[BrowserManager] 开始清理所有资源...');

    try {
      // 保存登录状态
      if (this.taobaoContext) {
        await this.saveLoginState('taobao');
      }
      if (this.xiaohongshuContext) {
        await this.saveLoginState('xiaohongshu');
      }

      // 关闭所有页面
      const allPages = [...this.taobaoPages, ...this.xiaohongshuPages];
      await Promise.allSettled(allPages.map(page => page.close()));
      
      // 清理页面池
      this.taobaoPages.clear();
      this.xiaohongshuPages.clear();

      // 关闭contexts
      if (this.taobaoContext) {
        await this.taobaoContext.close();
        this.taobaoContext = null;
      }
      if (this.xiaohongshuContext) {
        await this.xiaohongshuContext.close();
        this.xiaohongshuContext = null;
      }

      // 关闭浏览器
      if (this.browser) {
        await this.browser.close();
        this.browser = null;
      }

      console.log('[BrowserManager] ✅ 所有资源清理完成');
    } catch (error) {
      console.error('[BrowserManager] ❌ 资源清理过程中出错:', error);
    }
  }

  /**
   * 检查浏览器是否正在运行
   */
  public isRunning(): boolean {
    return this.browser !== null && this.browser.isConnected();
  }

  /**
   * 🎯 为任务启动浏览器（强制visible模式）
   * 这个方法专门用于任务执行时启动浏览器，确保用户可以看到爬虫操作过程
   */
  public async launchForTask(): Promise<void> {
    console.log('[BrowserManager] 🎯 为任务启动浏览器 - 强制使用visible模式');

    // 如果浏览器已经在运行，需要重启为visible模式
    if (this.browser) {
      console.log('[BrowserManager] 🔄 检测到浏览器已运行，重启为visible模式...');

      // 🔧 修复：添加更长的延迟，确保任务状态更新完成后再重启浏览器
      console.log('[BrowserManager] ⏳ 等待任务状态稳定...');
      await new Promise(resolve => setTimeout(resolve, 500));

      // 保存当前登录状态
      await this.saveAllLoginStates();
      await this.close();

      // 🔧 修复：重启后等待更长时间，确保资源完全释放
      console.log('[BrowserManager] ⏳ 等待资源释放完成...');
      await new Promise(resolve => setTimeout(resolve, 300));
    }

    // 启动浏览器，forTask=true强制使用visible模式
    await this.launch(true, true);
  }

  /**
   * 切换浏览器可视化模式
   * @param headless 是否使用headless模式
   *
   * ⚠️ 重要说明：
   * 由于Playwright的限制，浏览器实例一旦启动就无法动态切换headless模式。
   * 因此这个操作会：
   * 1. 保存当前登录状态和任务状态
   * 2. 关闭当前浏览器实例（会中断正在进行的任务）
   * 3. 使用新的headless设置重新启动浏览器
   * 4. 恢复登录状态
   *
   * 建议在任务执行间隙进行切换，避免中断正在进行的任务。
   */
  public async toggleVisualization(headless: boolean): Promise<{
    success: boolean;
    message: string;
    taskInterrupted: boolean
  }> {
    console.log(`[BrowserManager] 🔄 切换浏览器可视化模式: ${headless ? 'headless' : 'visible'}`);

    let taskInterrupted = false;

    try {
      // 检查是否有活跃的页面（可能正在执行任务）
      const activePagesCount = this.taobaoPages.size + this.xiaohongshuPages.size;
      if (activePagesCount > 0) {
        console.warn(`[BrowserManager] ⚠️ 检测到 ${activePagesCount} 个活跃页面，切换模式可能会中断正在进行的任务`);
        taskInterrupted = true;
      }

      // 保存当前登录状态
      if (this.browser) {
        await this.saveAllLoginStates();
        await this.close();
      }

      // 重新启动浏览器
      await this.launch(headless);

      const message = `浏览器已切换到${headless ? '后台' : '可视化'}模式${taskInterrupted ? '（任务已中断，需要重新启动）' : ''}`;
      console.log(`[BrowserManager] ✅ ${message}`);

      return {
        success: true,
        message,
        taskInterrupted
      };

    } catch (error) {
      console.error('[BrowserManager] ❌ 浏览器模式切换失败:', error);
      return {
        success: false,
        message: `浏览器模式切换失败: ${error instanceof Error ? error.message : 'Unknown error'}`,
        taskInterrupted: true
      };
    }
  }

  /**
   * 保存所有平台的登录状态
   */
  private async saveAllLoginStates(): Promise<void> {
    try {
      if (this.taobaoContext) {
        await this.saveLoginState('taobao');
      }
      if (this.xiaohongshuContext) {
        await this.saveLoginState('xiaohongshu');
      }
    } catch (error) {
      console.error('[BrowserManager] Failed to save login states:', error);
    }
  }

  /**
   * 检查指定平台的context是否可用
   */
  public isContextReady(platform: Platform): boolean {
    const context = platform === 'taobao' ? this.taobaoContext : this.xiaohongshuContext;
    return context !== null;
  }
}

 