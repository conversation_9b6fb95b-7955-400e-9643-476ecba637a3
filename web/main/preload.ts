import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';
import { Task } from '../types/task';

// EnhancedTaskMonitor 数据类型定义
interface WorkerStatus {
  id: string
  platform: string
  isHealthy: boolean
  isAvailable: boolean
  successCount: number
  failureCount: number
  successRate: string
  uptime: number
  lastActivity: number
}

interface SystemStatus {
  isOnline: boolean
  lastHeartbeat: Date
  activeConnections: number
  memoryUsage: number
  cpuUsage: number
  diskUsage: number
}

interface RealTimeStats {
  tasksProcessed: number
  successRate: number
  avgResponseTime: number
  errorsInLastHour: number
  activeWorkers: number
  queueLength: number
}

const electronAPI = {
  getTasks: (): Promise<Task[]> => ipcRenderer.invoke('get-tasks'),
  getTaskById: (taskId: string): Promise<Task | null> => ipcRenderer.invoke('get-task-by-id', taskId),
  startTask: (taskData: Partial<Task>): Promise<Task> => ipc<PERSON>enderer.invoke('start-task', taskData),
  deleteTask: (taskId: string, options?: { force?: boolean }): Promise<void> => ipcRenderer.invoke('delete-task', taskId, options),
  confirmKeywords: (data: { taskId: string; keywords: string[] }): Promise<Task | null> => ipcRenderer.invoke('confirm-keywords', data),
  resumeTask: (taskId: string): Promise<void> => ipcRenderer.invoke('resume-task', taskId),
  retryTask: (taskId: string): Promise<void> => ipcRenderer.invoke('retry-task', taskId),

  // EnhancedTaskMonitor 真实数据 API
  getWorkerStatuses: (taskId?: string): Promise<WorkerStatus[]> => ipcRenderer.invoke('get-worker-statuses', taskId),
  getSystemStatus: (): Promise<SystemStatus> => ipcRenderer.invoke('get-system-status'),
  getRealTimeStats: (): Promise<RealTimeStats> => ipcRenderer.invoke('get-realtime-stats'),
  controlTask: (taskId: string, action: 'pause' | 'resume' | 'stop'): Promise<void> => ipcRenderer.invoke('control-task', taskId, action),
  restartWorker: (workerId: string): Promise<void> => ipcRenderer.invoke('restart-worker', workerId),
  exportTaskData: (taskId: string, dataType: 'links' | 'comments' | 'insights' | 'all', format: 'excel' | 'csv' | 'json'): Promise<string> => ipcRenderer.invoke('export-task-data', taskId, dataType, format),

  onTasksUpdated: (callback: (tasks: Task[]) => void) => {
    const handler = (event: Electron.IpcRendererEvent, tasks: Task[]) => callback(tasks);
    ipcRenderer.on('tasks-updated', handler);
    return () => ipcRenderer.removeListener('tasks-updated', handler);
  },

  onTaskUpdated: (taskId: string, callback: (task: Task) => void) => {
    const channel = `task-updated:${taskId}`;
    const handler = (event: Electron.IpcRendererEvent, task: Task) => callback(task);
    ipcRenderer.on(channel, handler);
    return () => ipcRenderer.removeListener(channel, handler);
  },

  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel);
  },

  // V2.0 登录管理相关方法
  startLoginSession: (platform: string): Promise<{ success: boolean; message?: string; error?: string }> =>
    ipcRenderer.invoke('start-login-session', platform),

  checkLoginStatus: (): Promise<Array<{ platform: string; isLoggedIn: boolean; lastLoginTime?: string; sessionValid?: boolean; error?: string }>> =>
    ipcRenderer.invoke('check-login-status'),

  clearLoginSession: (platform: string): Promise<{ success: boolean; message?: string; error?: string }> =>
    ipcRenderer.invoke('clear-login-session', platform),

  // 🔥 智能文件解析方法
  parseFileForKeywords: (filePath: string): Promise<string[]> =>
    ipcRenderer.invoke('parse-file-for-keywords', filePath),

  // AI输出相关方法
  onAIOutput: (callback: (event: any, data: any) => void) => {
    const handler = (event: Electron.IpcRendererEvent, data: any) => callback(event, data);
    ipcRenderer.on('ai-output', handler);
    return () => ipcRenderer.removeListener('ai-output', handler);
  },

  onTaskProgress: (callback: (event: any, progress: any) => void) => {
    const handler = (event: Electron.IpcRendererEvent, progress: any) => callback(event, progress);
    ipcRenderer.on('task-progress', handler);
    return () => ipcRenderer.removeListener('task-progress', handler);
  },

  // 浏览器可视化控制
  toggleBrowserVisualization: (headless: boolean): Promise<{ success: boolean; message?: string; error?: string }> =>
    ipcRenderer.invoke('toggle-browser-visualization', headless),

  // 🔧 新增：通用事件监听方法
  on: (channel: string, callback: (event: any, ...args: any[]) => void) => {
    const handler = (event: Electron.IpcRendererEvent, ...args: any[]) => callback(event, ...args);
    ipcRenderer.on(channel, handler);
    return () => ipcRenderer.removeListener(channel, handler);
  },

  off: (channel: string, callback: (event: any, ...args: any[]) => void) => {
    ipcRenderer.removeListener(channel, callback);
  },
};

// V2.0 暴露两个别名以确保兼容性
contextBridge.exposeInMainWorld('electron', electronAPI);
contextBridge.exposeInMainWorld('electronAPI', electronAPI);