"use client"

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react"
import { useRouter } from "next/navigation"
import type { Task } from "@/types/task"
import { useTaskDetail } from "@/hooks/useTaskStore"
import { KeywordConfirmationModal } from "./KeywordConfirmationModal"
import { ReportContainer } from "./ReportContainer"
import { AIThinkingProcess } from "./AIThinkingProcess"
import { EnhancedTaskMonitor } from "./EnhancedTaskMonitor"
import { useSystemLog } from "./SystemLogPanel"
import { Button } from "@/components/ui/button"
import { RefreshCw, AlertCircle, CheckCircle, Clock, Search, Brain, Zap, Circle, FileUp, Users, Sparkles, Timer, ExternalLink, Monitor, Square } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { mapUIStatusToChinese } from "@/lib/task-status-mapping"

interface TaskMonitorProps {
  jobId: string
}

type StepStatus = "pending" | "active" | "completed" | "failed"

interface Step {
  key: string
  title: string
  Icon: React.ElementType
  description: string
  isQuickScanStep: boolean
}

// 按照PRD要求和真实后端状态定义的基础步骤
const BASE_STEPS: Step[] = [
  { key: "PENDING", title: "任务排队中", Icon: Clock, description: "任务正在队列中等待处理", isQuickScanStep: true },
  { key: "EXPENTING", title: "智能拓词中", Icon: Sparkles, description: "AI正在基于核心词扩展相关搜索关键词", isQuickScanStep: false },
  { key: "DISCOVERING", title: "链接发现中", Icon: Search, description: "在各大平台发现高价值商品/内容", isQuickScanStep: true },
  { key: "WAITING_CONFIRMATION", title: "等待用户确认", Icon: Users, description: "需要您审核并确认最终的关键词", isQuickScanStep: false },
  { key: "SCRAPING", title: "评论采集中", Icon: FileUp, description: "正在采集相关页面的用户评论数据", isQuickScanStep: true },
  { key: "ANALYZING", title: "AI洞察分析", Icon: Brain, description: "AI正在深度分析采集到的数据", isQuickScanStep: true },
  { key: "COMPLETED", title: "分析完成", Icon: CheckCircle, description: "报告已生成完成", isQuickScanStep: true },
]

// 动态干预步骤 - 根据实际情况插入
const INTERVENTION_STEPS: Step[] = [
  { key: "WAITING_USER_LOGIN", title: "等待用户登录", Icon: Users, description: "需要您登录淘宝账号以继续采集", isQuickScanStep: true },
  { key: "WAITING_CAPTCHA", title: "等待验证码", Icon: AlertCircle, description: "需要您完成验证码验证", isQuickScanStep: true },
]

// 任务控制状态步骤 - 取消等
const CONTROL_STEPS: Step[] = [
  { key: "CANCELLED", title: "任务已取消", Icon: Square, description: "任务已被用户取消", isQuickScanStep: true },
]

function getStepStatus(
  stepKey: string,
  currentStatus: Task["status"],
  taskStatus: Task["status"],
  stepsToRender: Step[]
): StepStatus {
  // 失败状态处理 - 修复逻辑
  if (taskStatus === "FAILED") {
    // 对于失败的任务，找到失败发生的步骤
    const statusOrder = stepsToRender.map(step => step.key);
    const currentIndex = statusOrder.indexOf(currentStatus);
    const stepIndex = statusOrder.indexOf(stepKey);

    // 如果当前状态不在步骤列表中，使用基础顺序判断
    if (currentIndex === -1) {
      const baseOrder = ["PENDING", "EXPENTING", "DISCOVERING", "WAITING_CONFIRMATION", "SCRAPING", "ANALYZING", "COMPLETED", "CANCELLED"];
      const baseCurrentIndex = baseOrder.indexOf(currentStatus);
      const baseStepIndex = baseOrder.indexOf(stepKey);

      if (baseStepIndex < baseCurrentIndex) return "completed";
      if (baseStepIndex === baseCurrentIndex) return "failed";
      return "pending";
    }

    // 失败前的步骤标记为完成，失败的步骤标记为失败，后续步骤为pending
    if (stepIndex < currentIndex) return "completed";
    if (stepIndex === currentIndex) return "failed";
    return "pending";
  }

  // 动态构建状态顺序，基于实际要渲染的步骤
  const statusOrder = stepsToRender.map(step => step.key);
  const currentIndex = statusOrder.indexOf(currentStatus);
  const stepIndex = statusOrder.indexOf(stepKey);

  // 如果当前状态不在列表中，可能是动态插入的干预状态
  if (currentIndex === -1) {
    // 如果当前步骤就是当前状态，则为active
    if (stepKey === currentStatus) return "active";
    // 否则根据基础状态顺序判断
    const baseOrder = ["PENDING", "EXPENTING", "DISCOVERING", "WAITING_CONFIRMATION", "SCRAPING", "ANALYZING", "COMPLETED", "CANCELLED"];
    const baseCurrentIndex = baseOrder.indexOf(currentStatus);
    const baseStepIndex = baseOrder.indexOf(stepKey);

    if (baseStepIndex < baseCurrentIndex) return "completed";
    if (baseStepIndex === baseCurrentIndex) return "active";
    return "pending";
  }

  if (stepIndex < currentIndex) return "completed";
  if (stepIndex === currentIndex) return "active";
  return "pending";
}

const StepItem = ({
  step,
  status,
  isLast,
  children,
}: {
  step: Step
  status: StepStatus
  isLast: boolean
  children?: React.ReactNode
}) => {
  const getIcon = () => {
    switch (status) {
      case "completed": return <CheckCircle className="h-6 w-6 text-green-500" />;
      case "active": {
        // 根据步骤类型使用不同颜色的呼吸灯动效
        if (step.key === 'ANALYZING') {
          return <step.Icon className="h-6 w-6 text-purple-500 animate-pulse" />;
        } else if (step.key === 'EXPENTING') {
          return <step.Icon className="h-6 w-6 text-amber-500 animate-pulse" />;
        } else if (step.key === 'WAITING_USER_LOGIN' || step.key === 'WAITING_CAPTCHA') {
          return <step.Icon className="h-6 w-6 text-amber-600 animate-pulse" />;
        }
        return <step.Icon className="h-6 w-6 text-blue-500 animate-pulse" />;
      }
      case "failed": return <AlertCircle className="h-6 w-6 text-red-500" />;
      case "pending": default: return <Circle className="h-6 w-6 text-gray-300" />;
    }
  }

  return (
    <div className="flex gap-4">
      {/* Icon and Connector */}
      <div className="flex flex-col items-center">
        {getIcon()}
        {!isLast && <div className={cn("w-px h-full mt-2",
          status === 'completed' ? 'bg-green-500' : 'bg-gray-200'
        )}></div>}
      </div>

      {/* Content */}
      <div className="flex-1 pb-12">
        <div className={cn("font-medium",
          status === 'active' && step.key === 'ANALYZING' && 'text-purple-600',
          status === 'active' && step.key === 'EXPENTING' && 'text-amber-600',
          status === 'active' && (step.key === 'WAITING_USER_LOGIN' || step.key === 'WAITING_CAPTCHA') && 'text-amber-600',
          status === 'active' && !['ANALYZING', 'EXPENTING', 'WAITING_USER_LOGIN', 'WAITING_CAPTCHA'].includes(step.key) && 'text-blue-600',
          status === 'completed' && 'text-gray-800',
          status === 'pending' && 'text-gray-400',
          status === 'failed' && 'text-red-600',
        )}>
          {step.title}
        </div>
        <div className={cn("text-sm",
          status === 'active' && step.key === 'ANALYZING' ? 'text-purple-500' :
            status === 'active' && step.key === 'EXPENTING' ? 'text-amber-500' :
              status === 'active' && (step.key === 'WAITING_USER_LOGIN' || step.key === 'WAITING_CAPTCHA') ? 'text-amber-500' :
                status === 'active' ? 'text-blue-500' : 'text-gray-500',
          status === 'failed' && 'text-red-500'
        )}>
          {children ? children : step.description}
        </div>
      </div>
    </div>
  )
}

export function TaskMonitor({ jobId }: TaskMonitorProps) {
  // 🔥 使用新的任务状态管理
  const { task, isLoading, notFound } = useTaskDetail(jobId)
  const router = useRouter()

  const [showKeywordModal, setShowKeywordModal] = useState(false)
  const [isConfirming, setIsConfirming] = useState(false)

  const [loginWaitStartTime, setLoginWaitStartTime] = useState<number | null>(null) // 登录等待开始时间
  const [loginWaitElapsed, setLoginWaitElapsed] = useState(0) // 登录等待已用时间
  const { addLog } = useSystemLog() // V2.0 日志功能

  // 🎯 实时进度状态监听
  const [realTimeProgress, setRealTimeProgress] = useState<{
    message?: string;
    phase?: string;
    uiStatus?: string;
    chineseStatus?: string;
    timestamp?: string;
  } | null>(null)

  // 🎯 监听增强的任务进度事件
  useEffect(() => {
    if (!task?.id) return;

    const handleProgressUpdate = (event: any) => {
      console.log(`[TaskMonitor] 📡 Received enhanced progress event:`, event);
      setRealTimeProgress({
        message: event.message,
        phase: event.phase,
        uiStatus: event.uiStatus,
        chineseStatus: event.chineseStatus,
        timestamp: event.timestamp
      });
    };

    // 监听增强的进度事件
    window.electronAPI?.on?.('task-progress', handleProgressUpdate);

    return () => {
      window.electronAPI?.off?.('task-progress', handleProgressUpdate);
    };
  }, [task?.id]);

  // 🔥 简化的任务不存在处理
  useEffect(() => {
    if (notFound) {
      console.log(`[TaskMonitor] Task ${jobId} not found, redirecting to home in 3 seconds`)
      const timer = setTimeout(() => {
        router.push('/app')
      }, 3000)

      return () => clearTimeout(timer)
    }
  }, [notFound, jobId, router])

  // 🔥 任务状态变化日志记录
  useEffect(() => {
    if (task) {
      addLog({
        level: 'info',
        category: 'task',
        message: `任务状态: ${task.status}`,
        taskId: jobId
      })

      if (task.progress?.message) {
        addLog({
          level: 'info',
          category: 'task',
          message: task.progress.message,
          taskId: jobId
        })
      }
    }
  }, [task?.status, task?.progress?.message, addLog, jobId])

  // 自动打开登录窗口 - 移到使用前定义
  const handleAutoOpenLogin = useCallback(async (platform: 'taobao' | 'xiaohongshu') => {
    try {
      addLog({
        level: 'info',
        category: 'login',
        message: `自动启动${platform === 'taobao' ? '淘宝' : '小红书'}登录窗口`,
        taskId: jobId
      })

      await window.electronAPI?.startLoginSession?.(platform)
    } catch (error) {
      console.error('Failed to auto open login:', error)
      addLog({
        level: 'error',
        category: 'login',
        message: `自动启动登录窗口失败: ${error}`,
        taskId: jobId
      })
    }
  }, [jobId, addLog])

  // 根据任务状态变化，决定是否显示关键词确认弹窗
  useEffect(() => {
    if (task?.status === "WAITING_CONFIRMATION") {
      setShowKeywordModal(true)
    } else {
      setShowKeywordModal(false)
    }

    // 🎯 浏览器模式现在自动管理，登录窗口会在任务执行时自动显示
    // 移除自动打开登录窗口的逻辑，因为visible模式下用户可以直接在浏览器中处理登录
  }, [task, handleAutoOpenLogin])

  // 监控登录等待时间
  useEffect(() => {
    if (task?.status === "WAITING_USER_LOGIN") {
      if (!loginWaitStartTime) {
        setLoginWaitStartTime(Date.now())
      }

      const timer = setInterval(() => {
        if (loginWaitStartTime) {
          setLoginWaitElapsed(Math.floor((Date.now() - loginWaitStartTime) / 1000))
        }
      }, 1000)

      return () => clearInterval(timer)
    } else {
      setLoginWaitStartTime(null)
      setLoginWaitElapsed(0)
    }
  }, [task?.status, loginWaitStartTime])

  // 🔥 处理任务不存在的情况
  useEffect(() => {
    if (notFound) {
      // 任务不存在时，强制关闭所有弹窗
      setShowKeywordModal(false)
      setIsConfirming(false)
    }
  }, [notFound])

  const handleKeywordConfirmation = useCallback(async (selectedKeywords: string[]) => {
    if (!task) {
      console.error('Cannot confirm keywords: task not found')
      setShowKeywordModal(false)
      return
    }

    setIsConfirming(true)
    try {
      await window.electronAPI.confirmKeywords({
        taskId: jobId,
        keywords: selectedKeywords,
      })
      setShowKeywordModal(false)
    } catch (error) {
      console.error("Failed to confirm keywords:", error)
      // 确认失败时也要关闭弹窗，避免卡死
      setShowKeywordModal(false)
    } finally {
      setIsConfirming(false)
    }
  }, [jobId, task])

  const handleKeywordModalClose = useCallback(() => {
    setShowKeywordModal(false)
    setIsConfirming(false)
  }, [])

  const handleRetry = useCallback(async () => {
    if (!task) {
      console.error('Cannot retry task: task not found')
      return
    }

    try {
      console.log(`[TaskMonitor] Retrying task ${jobId}`)
      // 调用重试任务的 IPC 方法
      await window.electronAPI.retryTask(jobId)
    } catch (error) {
      console.error("Failed to retry task:", error)
      // TODO: Show error toast to user
    }
  }, [jobId, task])

  const handleResumeTask = useCallback(async () => {
    if (!task) {
      console.error('Cannot resume task: task not found')
      return
    }

    try {
      console.log(`[TaskMonitor] Resuming task ${jobId}`)
      await window.electronAPI.resumeTask(jobId)
    } catch (error) {
      console.error("Failed to resume task:", error)
      // TODO: Show error toast to user
    }
  }, [jobId, task])

  // 🎯 浏览器模式现在自动管理：初始化时headless，任务时visible
  // 移除手动切换功能，系统会自动处理

  // 动态构建步骤列表的函数
  const buildStepsToRender = useMemo(() => {
    if (!task) return [];

    const { status, scanMode } = task;

    // 基础步骤过滤（根据扫描模式）
    let steps = BASE_STEPS.filter(step => scanMode === 'deep' || step.isQuickScanStep);

    // 动态插入干预步骤
    const finalSteps: Step[] = [];

    for (let i = 0; i < steps.length; i++) {
      const currentStep = steps[i];
      finalSteps.push(currentStep);

      // 在DISCOVERING步骤后，根据当前状态动态插入干预步骤
      if (currentStep.key === "DISCOVERING") {
        // 如果当前状态是干预状态，插入对应的干预步骤
        if (status === "WAITING_USER_LOGIN") {
          const loginStep = INTERVENTION_STEPS.find(s => s.key === "WAITING_USER_LOGIN");
          if (loginStep) finalSteps.push(loginStep);
        } else if (status === "WAITING_CAPTCHA") {
          const captchaStep = INTERVENTION_STEPS.find(s => s.key === "WAITING_CAPTCHA");
          if (captchaStep) finalSteps.push(captchaStep);
        }
      }
    }

    // 处理控制状态（取消）- 这些状态会替换正常流程
    if (status === "CANCELLED") {
      const controlStep = CONTROL_STEPS.find(s => s.key === status);
      if (controlStep) {
        // 对于控制状态，显示到当前步骤为止，然后显示控制状态
        finalSteps.push(controlStep);
      }
    }

    return finalSteps;
  }, [task]);

  const renderContent = () => {
    if (isLoading) {
      return <div className="text-center py-10">任务正在初始化，请稍候...</div>
    }

    if (!task) {
      if (notFound) {
        return <div className="text-center py-10 text-red-500">任务未找到，3秒后将自动返回主页。</div>
      } else {
        return <div className="text-center py-10">任务正在加载，请稍候...</div>
      }
    }

    if (task.status === "COMPLETED" && task.reportData) {
      return <ReportContainer report={task.reportData} />;
    }

    // ====== 真实进度反映核心逻辑 ======
    const { status, scanMode, progress, errorMessage } = task;

    // 使用动态构建的步骤列表
    const stepsToRender = buildStepsToRender;

    // 映射真实任务状态到显示状态
    let displayStatus = status;

    // 如果是DISCOVERING状态但没有finalKeywords，可能还在拓词阶段
    if (status === "DISCOVERING" && scanMode === "deep" && !task.finalKeywords && !task.processedKeywords) {
      displayStatus = "EXPENTING";
    }

    return (
      <Card className="w-full max-w-3xl mx-auto my-8">
        <CardHeader>
          <CardTitle>任务追踪</CardTitle>
        </CardHeader>
        <CardContent className="p-6 md:p-8">
          {stepsToRender.map((step, index) => {
            const stepStatus = getStepStatus(step.key, displayStatus, status, stepsToRender);
            const isActive = stepStatus === 'active';

            let descriptionOverride: React.ReactNode = null;

            // 优先处理失败状态的展示
            if (status === 'FAILED' && stepStatus === 'failed') {
              descriptionOverride = (
                <div className="flex flex-col gap-2">
                  <span className="text-red-500">{errorMessage || "未知错误"}</span>
                  <Button variant="outline" size="sm" onClick={handleRetry} className="gap-1 w-fit">
                    <RefreshCw className="w-4 h-4" /> 重试
                  </Button>
                </div>
              );
            } else if (isActive) {
              // 🎯 优先使用实时进度信息，回退到任务进度信息
              const currentMessage = realTimeProgress?.message || progress?.message;
              const currentChineseStatus = realTimeProgress?.chineseStatus;

              // 根据PRD要求显示真实进度信息
              if (step.key === 'PENDING') {
                descriptionOverride = currentMessage || "任务正在队列中等待处理...";
              } else if (step.key === 'EXPENTING') {
                descriptionOverride = currentMessage || "AI正在基于核心词扩展相关搜索关键词...";
              } else if (step.key === 'DISCOVERING') {
                // 显示真实的链接发现进度
                if (progress?.discovered !== undefined && progress?.discovered_total !== undefined) {
                  descriptionOverride = (
                    <div className="flex flex-col gap-2">
                      <span>{currentMessage || `正在发现商品链接，已找到 ${progress.discovered} / ${progress.discovered_total} 个`}</span>
                      <Progress value={(progress.discovered / progress.discovered_total) * 100} className="w-full" />
                    </div>
                  );
                } else {
                  descriptionOverride = currentMessage || "在各大平台发现高价值商品/内容...";
                }
              } else if (step.key === 'WAITING_USER_LOGIN') {
                const formatTime = (seconds: number) => {
                  const mins = Math.floor(seconds / 60)
                  const secs = seconds % 60
                  return mins > 0 ? `${mins}分${secs}秒` : `${secs}秒`
                }

                const estimatedTime = "通常需要1-3分钟"
                const isLongWait = loginWaitElapsed > 180 // 超过3分钟

                descriptionOverride = (
                  <div className="flex flex-col gap-3">
                    <div className="flex items-center gap-2">
                      <span className="text-amber-600 font-medium">正在等待登录完成...</span>
                      {loginWaitElapsed > 0 && (
                        <span className="text-sm text-gray-500 flex items-center gap-1">
                          <Timer className="w-4 h-4" />
                          已等待 {formatTime(loginWaitElapsed)}
                        </span>
                      )}
                    </div>

                    <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
                      <div className="flex items-start gap-2">
                        <ExternalLink className="w-4 h-4 text-amber-600 mt-0.5 flex-shrink-0" />
                        <div className="text-sm">
                          <p className="text-amber-800 font-medium mb-1">请在浏览器中完成登录</p>
                          <p className="text-amber-700 mb-2">
                            系统已自动为您打开登录窗口，请在浏览器中登录您的淘宝账号
                          </p>
                          <p className="text-amber-600 text-xs">
                            预计用时：{estimatedTime}
                            {isLongWait && " • 如遇到问题，请尝试刷新页面或重新登录"}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleResumeTask}
                        className="gap-1"
                      >
                        <Zap className="w-4 h-4" /> 登录完成，继续任务
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleAutoOpenLogin('taobao')}
                        className="gap-1 text-amber-600 hover:bg-amber-50"
                      >
                        <ExternalLink className="w-4 h-4" /> 重新打开登录窗口
                      </Button>
                    </div>
                  </div>
                );
              } else if (step.key === 'WAITING_CAPTCHA') {
                descriptionOverride = (
                  <div className="flex flex-col gap-2">
                    <span className="text-amber-600">请完成验证码验证</span>
                    <span className="text-sm text-gray-500">系统正在等待您完成人机验证</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleResumeTask}
                      className="gap-1 w-fit mt-2"
                    >
                      <Zap className="w-4 h-4" /> 验证完成，继续任务
                    </Button>
                  </div>
                );
              } else if (step.key === 'WAITING_CONFIRMATION') {
                descriptionOverride = "需要您审核并确认最终的关键词";
              } else if (step.key === 'SCRAPING') {
                // 显示真实的评论采集进度
                if (progress?.scraped !== undefined && progress?.scraped_total !== undefined) {
                  descriptionOverride = (
                    <div className="flex flex-col gap-2">
                      <span>正在采集第 {progress.scraped} / {progress.scraped_total} 个商品评论</span>
                      <Progress value={(progress.scraped / progress.scraped_total) * 100} className="w-full" />
                    </div>
                  );
                } else {
                  descriptionOverride = progress?.message || "正在采集相关页面的用户评论数据...";
                }
              } else if (step.key === 'ANALYZING') {
                descriptionOverride = progress?.message || "AI正在深度分析采集到的数据...";
              } else if (step.key === 'COMPLETED') {
                descriptionOverride = "分析报告已生成完成";
              }
            }

            return (
              <StepItem key={step.key} step={step} status={stepStatus} isLast={index === stepsToRender.length - 1}>
                {descriptionOverride}
              </StepItem>
            );
          })}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="h-full flex flex-col">
      {/* AI过程跟随窗口 - 主界面 */}
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
          <Brain className="h-6 w-6 text-blue-500" />
          分析过程详情
        </h1>
        <div className="flex items-center gap-2">
          {/* 任务状态显示 */}
          {task && (
            <Badge variant={task.status === 'COMPLETED' ? 'default' : 'secondary'}>
              {task.status}
            </Badge>
          )}

          {/* 浏览器模式提示 */}
          <div className="flex items-center gap-1 text-sm text-gray-500">
            <Monitor className="h-4 w-4" />
            <span>浏览器已自动切换为可视化模式</span>
          </div>
        </div>
      </div>

      {/* 主内容区域 - 分为左右两栏 */}
      <div className="flex-1 flex gap-4 min-h-0">
        {/* 左侧：AI过程展示区 */}
        <div className="flex-1 flex flex-col min-w-0">
          <Card className="flex-1 flex flex-col">
            <CardContent className="flex-1 p-0">
              {/* AI思考过程面板 - 展示AI的思考过程 */}
              <AIThinkingProcess
                taskId={jobId}
                className="h-full border-0 shadow-none"
                maxHeight="100%"
                showControls={false}
              />
            </CardContent>
          </Card>
        </div>

        {/* 右侧：增强监控面板 */}
        <div className="w-80 flex flex-col">
          <EnhancedTaskMonitor
            task={task}
            className="h-full"
            showFollowTab={true}
            showDownloadTab={true}
          />
        </div>
      </div>

      {/* 任务完成时显示报告 */}
      {task?.status === "COMPLETED" && task.reportData && (
        <div className="mt-4">
          <ReportContainer report={task.reportData} />
        </div>
      )}

      {showKeywordModal && task?.processedKeywords && (
        <KeywordConfirmationModal
          isOpen={showKeywordModal}
          isLoading={isConfirming}
          onConfirm={handleKeywordConfirmation}
          onClose={handleKeywordModalClose}
          keywords={task.processedKeywords}
        />
      )}
    </div>
  )
}
